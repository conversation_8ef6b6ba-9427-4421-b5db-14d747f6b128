#pragma once

#include <string>
#include <cstddef>

namespace logger_sdk {

/**
 * @brief 日志级别枚举
 */
enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    CRITICAL = 5,
    OFF = 6
};

/**
 * @brief 异步溢出策略
 */
enum class AsyncOverflowPolicy {
    BLOCK,           // 阻塞等待队列有空间
    OVERRUN_OLDEST,  // 覆盖最旧的消息
    DISCARD_NEW      // 丢弃新消息
};

/**
 * @brief 日志配置结构体
 */
struct LoggerConfig {
    // 基本配置
    std::string logger_name = "default_logger";
    std::string log_file_path = "logs/app.log";
    LogLevel level = LogLevel::INFO;
    
    // 文件轮转配置
    size_t max_file_size = 10 * 1024 * 1024;  // 10MB
    size_t max_files = 5;                      // 保留5个文件
    bool rotate_on_open = false;               // 启动时是否轮转
    
    // 异步配置
    size_t async_queue_size = 8192;            // 异步队列大小
    size_t thread_pool_size = 1;               // 线程池大小
    AsyncOverflowPolicy overflow_policy = AsyncOverflowPolicy::BLOCK;
    
    // 格式化配置
    std::string pattern = "[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v";
    
    // 构造函数
    LoggerConfig() = default;
    
    /**
     * @brief 创建默认配置
     */
    static LoggerConfig create_default();
    
    /**
     * @brief 创建文件轮转配置
     * @param log_file 日志文件路径
     * @param max_size 最大文件大小（字节）
     * @param max_count 最大文件数量
     */
    static LoggerConfig create_rotating(const std::string& log_file, 
                                       size_t max_size, 
                                       size_t max_count);
    
    /**
     * @brief 验证配置有效性
     * @return true 如果配置有效
     */
    bool validate() const;
};

} // namespace logger_sdk
