cmake_minimum_required(VERSION 3.16)
project(spdlog_sdk VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    add_compile_options(/W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 创建spdlog库
set(SPDLOG_SOURCES
    thirdparty/spdlog/src/async.cpp
    thirdparty/spdlog/src/bundled_fmtlib_format.cpp
    thirdparty/spdlog/src/cfg.cpp
    thirdparty/spdlog/src/color_sinks.cpp
    thirdparty/spdlog/src/file_sinks.cpp
    thirdparty/spdlog/src/spdlog.cpp
    thirdparty/spdlog/src/stdout_sinks.cpp
)

add_library(spdlog STATIC ${SPDLOG_SOURCES})

# 设置spdlog包含目录
target_include_directories(spdlog
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/spdlog/include>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/sdk/inc>
        $<INSTALL_INTERFACE:include>
)

# 设置spdlog编译选项
target_compile_definitions(spdlog
    PUBLIC
        SPDLOG_COMPILED_LIB
    PRIVATE
        SPDLOG_COMPILED_LIB
)

# SDK库
set(SDK_SOURCES
    sdk/src/logger_config.cpp
    sdk/src/logger_sdk.cpp
)

set(SDK_HEADERS
    sdk/inc/logger_config.h
    sdk/inc/logger_sdk.h
    sdk/inc/logger_sdk_impl.h
)

# 创建SDK静态库
add_library(logger_sdk STATIC ${SDK_SOURCES} ${SDK_HEADERS})

# 设置包含目录
target_include_directories(logger_sdk
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/sdk/inc>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/spdlog/include
)

# 链接spdlog
target_link_libraries(logger_sdk
    PUBLIC
        spdlog
)

# 设置编译定义
target_compile_definitions(logger_sdk
    PRIVATE
        SPDLOG_COMPILED_LIB
)

# 创建示例程序
add_executable(logger_example examples/example.cpp)
target_link_libraries(logger_example logger_sdk)

# 创建测试程序
add_executable(logger_test examples/test.cpp)
target_link_libraries(logger_test logger_sdk)

# 安装配置
install(TARGETS logger_sdk
    EXPORT logger_sdk_targets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY sdk/inc/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

install(EXPORT logger_sdk_targets
    FILE logger_sdk_targets.cmake
    NAMESPACE logger_sdk::
    DESTINATION lib/cmake/logger_sdk
)

# 生成配置文件
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    logger_sdk_config_version.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/cmake/logger_sdk_config.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/logger_sdk_config.cmake
    INSTALL_DESTINATION lib/cmake/logger_sdk
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/logger_sdk_config.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/logger_sdk_config_version.cmake
    DESTINATION lib/cmake/logger_sdk
)
