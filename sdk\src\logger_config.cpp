#include "logger_config.h"
#include <algorithm>

namespace logger_sdk {

LoggerConfig LoggerConfig::create_default() {
    LoggerConfig config;
    config.logger_name = "default_logger";
    config.log_file_path = "logs/app.log";
    config.level = LogLevel::INFO;
    config.max_file_size = 10 * 1024 * 1024;  // 10MB
    config.max_files = 5;
    config.rotate_on_open = false;
    config.async_queue_size = 8192;
    config.thread_pool_size = 1;
    config.overflow_policy = AsyncOverflowPolicy::BLOCK;
    config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v";
    return config;
}

LoggerConfig LoggerConfig::create_rotating(const std::string& log_file, 
                                          size_t max_size, 
                                          size_t max_count) {
    LoggerConfig config = create_default();
    config.log_file_path = log_file;
    config.max_file_size = max_size;
    config.max_files = max_count;
    return config;
}

bool LoggerConfig::validate() const {
    // 检查logger名称
    if (logger_name.empty()) {
        return false;
    }
    
    // 检查日志文件路径
    if (log_file_path.empty()) {
        return false;
    }
    
    // 检查文件大小限制
    if (max_file_size == 0) {
        return false;
    }
    
    // 检查文件数量限制
    if (max_files == 0 || max_files > 1000) {
        return false;
    }
    
    // 检查异步队列大小
    if (async_queue_size == 0) {
        return false;
    }
    
    // 检查线程池大小
    if (thread_pool_size == 0 || thread_pool_size > 100) {
        return false;
    }
    
    // 检查日志级别
    if (static_cast<int>(level) < 0 || static_cast<int>(level) > 6) {
        return false;
    }
    
    return true;
}

} // namespace logger_sdk
