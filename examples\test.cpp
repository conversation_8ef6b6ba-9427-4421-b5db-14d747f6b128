#include "logger_sdk.h"
#include <iostream>
#include <cassert>
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>

using namespace logger_sdk;

// 简单的测试框架
class TestRunner {
public:
    static void run_test(const std::string& test_name, std::function<void()> test_func) {
        std::cout << "Running test: " << test_name << " ... ";
        try {
            test_func();
            std::cout << "PASSED" << std::endl;
            passed_++;
        } catch (const std::exception& e) {
            std::cout << "FAILED: " << e.what() << std::endl;
            failed_++;
        }
        total_++;
    }
    
    static void print_summary() {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Total: " << total_ << std::endl;
        std::cout << "Passed: " << passed_ << std::endl;
        std::cout << "Failed: " << failed_ << std::endl;
        std::cout << "Success Rate: " << (passed_ * 100.0 / total_) << "%" << std::endl;
    }
    
private:
    static int total_;
    static int passed_;
    static int failed_;
};

int TestRunner::total_ = 0;
int TestRunner::passed_ = 0;
int TestRunner::failed_ = 0;

// 测试配置验证
void test_config_validation() {
    // 测试有效配置
    auto valid_config = LoggerConfig::create_default();
    assert(valid_config.validate());
    
    // 测试无效配置
    LoggerConfig invalid_config;
    invalid_config.logger_name = "";  // 空名称
    assert(!invalid_config.validate());
    
    invalid_config = LoggerConfig::create_default();
    invalid_config.max_file_size = 0;  // 无效文件大小
    assert(!invalid_config.validate());
    
    invalid_config = LoggerConfig::create_default();
    invalid_config.max_files = 0;  // 无效文件数量
    assert(!invalid_config.validate());
}

// 测试基本初始化
void test_basic_initialization() {
    LoggerSDK logger;
    
    // 初始状态应该是未初始化
    assert(!logger.is_initialized());
    
    // 使用有效配置初始化
    auto config = LoggerConfig::create_default();
    config.log_file_path = "test_logs/init_test.log";
    
    bool result = logger.initialize(config);
    assert(result);
    assert(logger.is_initialized());
    
    // 重复初始化应该失败
    assert(!logger.initialize(config));
    
    logger.shutdown();
    assert(!logger.is_initialized());
}

// 测试日志级别设置
void test_log_levels() {
    LoggerSDK logger;
    auto config = LoggerConfig::create_default();
    config.log_file_path = "test_logs/level_test.log";
    config.level = LogLevel::WARN;
    
    assert(logger.initialize(config));
    
    // 测试级别设置
    assert(logger.get_level() == LogLevel::WARN);
    
    logger.set_level(LogLevel::DEBUG);
    assert(logger.get_level() == LogLevel::DEBUG);
    
    logger.set_level(LogLevel::ERROR);
    assert(logger.get_level() == LogLevel::ERROR);
    
    logger.shutdown();
}

// 测试文件创建
void test_file_creation() {
    // 清理测试文件
    std::filesystem::remove_all("test_logs");
    
    LoggerSDK logger;
    auto config = LoggerConfig::create_default();
    config.log_file_path = "test_logs/subdir/file_test.log";
    
    assert(logger.initialize(config));
    
    // 写入一些日志
    logger.info("Test message");
    logger.flush();
    
    // 检查文件是否创建
    assert(std::filesystem::exists("test_logs/subdir/file_test.log"));
    
    logger.shutdown();
}

// 测试日志写入
void test_log_writing() {
    LoggerSDK logger;
    auto config = LoggerConfig::create_default();
    config.log_file_path = "test_logs/write_test.log";
    config.level = LogLevel::DEBUG;
    
    assert(logger.initialize(config));
    
    // 写入不同级别的日志
    logger.debug("Debug message");
    logger.info("Info message");
    logger.warn("Warning message");
    logger.error("Error message");
    logger.critical("Critical message");
    
    // 测试格式化日志
    logger.info("Formatted message: {} + {} = {}", 1, 2, 3);
    
    logger.flush();
    
    // 检查文件内容
    std::ifstream file("test_logs/write_test.log");
    assert(file.is_open());
    
    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());
    
    assert(content.find("Debug message") != std::string::npos);
    assert(content.find("Info message") != std::string::npos);
    assert(content.find("Warning message") != std::string::npos);
    assert(content.find("Error message") != std::string::npos);
    assert(content.find("Critical message") != std::string::npos);
    assert(content.find("1 + 2 = 3") != std::string::npos);
    
    logger.shutdown();
}

// 测试文件轮转
void test_file_rotation() {
    LoggerSDK logger;
    auto config = LoggerConfig::create_rotating(
        "test_logs/rotation_test.log",
        512,  // 512字节
        3     // 3个文件
    );
    
    assert(logger.initialize(config));
    
    // 写入足够的数据触发轮转
    for (int i = 0; i < 50; ++i) {
        logger.info("This is a long message to trigger rotation: message number {}", i);
    }
    
    // 手动触发轮转
    logger.rotate_now();
    logger.flush();
    
    // 检查轮转文件是否存在
    assert(std::filesystem::exists("test_logs/rotation_test.log"));
    
    logger.shutdown();
}

// 测试异步队列统计
void test_queue_stats() {
    LoggerSDK logger;
    auto config = LoggerConfig::create_default();
    config.log_file_path = "test_logs/stats_test.log";
    config.async_queue_size = 1024;
    
    assert(logger.initialize(config));
    
    // 获取初始统计
    auto stats = logger.get_queue_stats();
    assert(stats.queue_size >= 0);
    assert(stats.overrun_counter >= 0);
    assert(stats.discard_counter >= 0);
    
    // 写入一些日志
    for (int i = 0; i < 10; ++i) {
        logger.info("Stats test message {}", i);
    }
    
    // 再次获取统计
    stats = logger.get_queue_stats();
    // 队列统计应该是有效的
    
    logger.shutdown();
}

// 测试多线程安全性
void test_thread_safety() {
    LoggerSDK logger;
    auto config = LoggerConfig::create_default();
    config.log_file_path = "test_logs/thread_test.log";
    
    assert(logger.initialize(config));
    
    std::vector<std::thread> threads;
    const int num_threads = 4;
    const int messages_per_thread = 50;
    
    // 创建多个线程同时写日志
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&logger, t, messages_per_thread]() {
            for (int i = 0; i < messages_per_thread; ++i) {
                logger.info("Thread {} message {}", t, i);
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    logger.flush();
    
    // 检查文件是否存在且有内容
    assert(std::filesystem::exists("test_logs/thread_test.log"));
    assert(std::filesystem::file_size("test_logs/thread_test.log") > 0);
    
    logger.shutdown();
}

int main() {
    std::cout << "Running Logger SDK Tests" << std::endl;
    std::cout << "========================" << std::endl;
    
    // 清理测试目录
    std::filesystem::remove_all("test_logs");
    
    // 运行所有测试
    TestRunner::run_test("Config Validation", test_config_validation);
    TestRunner::run_test("Basic Initialization", test_basic_initialization);
    TestRunner::run_test("Log Levels", test_log_levels);
    TestRunner::run_test("File Creation", test_file_creation);
    TestRunner::run_test("Log Writing", test_log_writing);
    TestRunner::run_test("File Rotation", test_file_rotation);
    TestRunner::run_test("Queue Stats", test_queue_stats);
    TestRunner::run_test("Thread Safety", test_thread_safety);
    
    TestRunner::print_summary();
    
    return TestRunner::failed_ > 0 ? 1 : 0;
}
