# spdlog SDK

基于 spdlog 的高性能异步日志 SDK，专门封装了 spdlog 的 rotate sink 异步模式。

## 特性

- **异步日志记录**: 基于 spdlog 的异步线程池，高性能非阻塞日志记录
- **文件轮转**: 支持按文件大小自动轮转，保持磁盘空间可控
- **线程安全**: 完全线程安全，支持多线程并发日志记录
- **简化接口**: 提供简洁易用的 C++ 接口，隐藏 spdlog 复杂性
- **配置灵活**: 支持丰富的配置选项，满足不同场景需求
- **资源管理**: 自动管理 logger 生命周期和资源清理

## 快速开始

### 基本使用

```cpp
#include "logger_sdk.h"

using namespace logger_sdk;

int main() {
    // 创建配置
    auto config = LoggerConfig::create_default();
    config.log_file_path = "logs/app.log";
    config.level = LogLevel::INFO;
    
    // 初始化日志系统
    auto& logger = LoggerSDK::instance();
    if (!logger.initialize(config)) {
        std::cerr << "Failed to initialize logger" << std::endl;
        return 1;
    }
    
    // 记录日志
    logger.info("Application started");
    logger.warn("This is a warning message");
    logger.error("Error occurred: {}", "file not found");
    
    // 刷新并关闭
    logger.flush();
    logger.shutdown();
    
    return 0;
}
```

### 文件轮转配置

```cpp
// 创建轮转配置：10MB文件大小，保留5个文件
auto config = LoggerConfig::create_rotating(
    "logs/app.log", 
    10 * 1024 * 1024,  // 10MB
    5                   // 保留5个文件
);

LoggerSDK logger;
logger.initialize(config);

// 手动触发轮转
logger.rotate_now();
```

### 高性能异步配置

```cpp
auto config = LoggerConfig::create_default();
config.async_queue_size = 16384;  // 更大的队列
config.thread_pool_size = 2;      // 2个工作线程
config.overflow_policy = AsyncOverflowPolicy::OVERRUN_OLDEST;

LoggerSDK logger;
logger.initialize(config);

// 高速日志记录
for (int i = 0; i < 10000; ++i) {
    logger.info("High speed log message {}", i);
}
```

## 构建

### 要求

- C++17 或更高版本
- CMake 3.16 或更高版本
- 支持的编译器：GCC 7+, Clang 6+, MSVC 2019+

### 构建步骤

```bash
# 克隆项目
git clone <repository_url>
cd spdlog_sdk

# 创建构建目录
mkdir build && cd build

# 配置和构建
cmake ..
cmake --build .

# 运行示例
./bin/logger_example

# 运行测试
./bin/logger_test
```

### Windows (Visual Studio)

```cmd
mkdir build && cd build
cmake .. -G "Visual Studio 16 2019"
cmake --build . --config Release
```

## API 参考

### LoggerConfig

日志配置类，包含所有日志系统配置选项。

#### 主要配置项

- `logger_name`: Logger 名称
- `log_file_path`: 日志文件路径
- `level`: 日志级别 (TRACE, DEBUG, INFO, WARN, ERROR, CRITICAL, OFF)
- `max_file_size`: 最大文件大小（字节）
- `max_files`: 最大文件数量
- `async_queue_size`: 异步队列大小
- `thread_pool_size`: 线程池大小
- `overflow_policy`: 溢出策略 (BLOCK, OVERRUN_OLDEST, DISCARD_NEW)
- `pattern`: 日志格式模式

#### 静态方法

- `create_default()`: 创建默认配置
- `create_rotating(file, size, count)`: 创建轮转配置

### LoggerSDK

主要的日志记录类。

#### 核心方法

- `initialize(config)`: 初始化日志系统
- `shutdown()`: 关闭日志系统
- `is_initialized()`: 检查初始化状态
- `set_level(level)`: 设置日志级别
- `get_level()`: 获取当前日志级别
- `flush()`: 刷新日志缓冲区
- `rotate_now()`: 手动触发文件轮转

#### 日志记录方法

- `trace(msg)`, `debug(msg)`, `info(msg)`, `warn(msg)`, `error(msg)`, `critical(msg)`
- 支持格式化：`info("User {} logged in", username)`

#### 单例访问

```cpp
auto& logger = LoggerSDK::instance();
```

### 队列统计

```cpp
auto stats = logger.get_queue_stats();
std::cout << "Queue size: " << stats.queue_size << std::endl;
std::cout << "Overrun count: " << stats.overrun_counter << std::endl;
std::cout << "Discard count: " << stats.discard_counter << std::endl;
```

## 日志格式

默认日志格式：`[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v`

示例输出：
```
[2024-01-15 14:30:25.123] [info] [12345] Application started
[2024-01-15 14:30:25.124] [warn] [12345] This is a warning message
[2024-01-15 14:30:25.125] [error] [12345] Error occurred: file not found
```

## 性能特性

- **异步处理**: 日志记录操作不阻塞主线程
- **批量写入**: 内部批量处理提高 I/O 效率
- **内存池**: 减少内存分配开销
- **无锁队列**: 高效的生产者-消费者模式

## 线程安全

- 所有公共接口都是线程安全的
- 支持多线程并发日志记录
- 内部使用适当的同步机制保证数据一致性

## 错误处理

- 初始化失败时返回 false
- 配置验证确保参数有效性
- 异常情况下自动资源清理
- 详细的错误信息输出

## 许可证

本项目基于 MIT 许可证开源。详见 LICENSE 文件。
