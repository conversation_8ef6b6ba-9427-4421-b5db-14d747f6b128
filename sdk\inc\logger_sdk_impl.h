#pragma once

#include "logger_sdk.h"
#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/sinks/rotating_file_sink.h>

namespace logger_sdk {

template<typename... Args>
void LoggerSDK::trace(const std::string& fmt, Args&&... args) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_ && logger_->should_log(spdlog::level::trace)) {
        logger_->trace(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void LoggerSDK::debug(const std::string& fmt, Args&&... args) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_ && logger_->should_log(spdlog::level::debug)) {
        logger_->debug(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void LoggerSDK::info(const std::string& fmt, Args&&... args) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_ && logger_->should_log(spdlog::level::info)) {
        logger_->info(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void LoggerSDK::warn(const std::string& fmt, Args&&... args) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_ && logger_->should_log(spdlog::level::warn)) {
        logger_->warn(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void LoggerSDK::error(const std::string& fmt, Args&&... args) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_ && logger_->should_log(spdlog::level::err)) {
        logger_->error(fmt, std::forward<Args>(args)...);
    }
}

template<typename... Args>
void LoggerSDK::critical(const std::string& fmt, Args&&... args) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_ && logger_->should_log(spdlog::level::critical)) {
        logger_->critical(fmt, std::forward<Args>(args)...);
    }
}

} // namespace logger_sdk
