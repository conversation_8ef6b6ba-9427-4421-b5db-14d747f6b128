#pragma once

#include "logger_config.h"
#include <memory>
#include <string>
#include <mutex>

// 前向声明
namespace spdlog {
    class async_logger;
    namespace details {
        class thread_pool;
    }
}

namespace logger_sdk {

/**
 * @brief 日志SDK主类
 * 
 * 这个类封装了spdlog的异步rotate sink功能，提供简化的接口
 * 支持异步日志记录、文件轮转、线程安全等特性
 */
class LoggerSDK {
public:
    /**
     * @brief 构造函数
     */
    LoggerSDK();
    
    /**
     * @brief 析构函数
     */
    ~LoggerSDK();
    
    // 禁用拷贝构造和赋值
    LoggerSDK(const LoggerSDK&) = delete;
    LoggerSDK& operator=(const LoggerSDK&) = delete;
    
    /**
     * @brief 初始化日志系统
     * @param config 日志配置
     * @return true 如果初始化成功
     */
    bool initialize(const LoggerConfig& config);
    
    /**
     * @brief 关闭日志系统
     */
    void shutdown();
    
    /**
     * @brief 检查是否已初始化
     */
    bool is_initialized() const;
    
    /**
     * @brief 设置日志级别
     * @param level 新的日志级别
     */
    void set_level(LogLevel level);
    
    /**
     * @brief 获取当前日志级别
     */
    LogLevel get_level() const;
    
    /**
     * @brief 刷新日志缓冲区
     */
    void flush();
    
    /**
     * @brief 手动触发文件轮转
     */
    void rotate_now();
    
    // 日志记录接口
    void trace(const std::string& msg);
    void debug(const std::string& msg);
    void info(const std::string& msg);
    void warn(const std::string& msg);
    void error(const std::string& msg);
    void critical(const std::string& msg);
    
    // 格式化日志记录接口
    template<typename... Args>
    void trace(const std::string& fmt, Args&&... args);
    
    template<typename... Args>
    void debug(const std::string& fmt, Args&&... args);
    
    template<typename... Args>
    void info(const std::string& fmt, Args&&... args);
    
    template<typename... Args>
    void warn(const std::string& fmt, Args&&... args);
    
    template<typename... Args>
    void error(const std::string& fmt, Args&&... args);
    
    template<typename... Args>
    void critical(const std::string& fmt, Args&&... args);
    
    /**
     * @brief 获取单例实例
     */
    static LoggerSDK& instance();
    
    /**
     * @brief 获取异步队列统计信息
     */
    struct QueueStats {
        size_t queue_size;
        size_t overrun_counter;
        size_t discard_counter;
    };
    
    QueueStats get_queue_stats() const;

private:
    mutable std::mutex mutex_;
    std::shared_ptr<spdlog::async_logger> logger_;
    std::shared_ptr<spdlog::details::thread_pool> thread_pool_;
    LoggerConfig config_;
    bool initialized_;
    
    // 内部辅助方法
    bool create_directories(const std::string& file_path);
    spdlog::level::level_enum to_spdlog_level(LogLevel level) const;
    LogLevel from_spdlog_level(spdlog::level::level_enum level) const;
};

} // namespace logger_sdk

// 包含模板实现
#include "logger_sdk_impl.h"
