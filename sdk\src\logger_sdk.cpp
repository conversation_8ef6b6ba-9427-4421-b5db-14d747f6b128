#include "logger_sdk.h"
#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/async_logger.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/details/thread_pool.h>
#include <spdlog/details/os.h>
#include <filesystem>
#include <iostream>

namespace logger_sdk {

LoggerSDK::LoggerSDK()
    : initialized_(false) {
}

LoggerSDK::~LoggerSDK() {
    shutdown();
}

bool LoggerSDK::initialize(const LoggerConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);

    // 检查是否已经初始化
    if (initialized_) {
        return false;
    }

    // 验证配置
    if (!config.validate()) {
        return false;
    }

    try {
        // 保存配置
        config_ = config;

        // 创建日志目录
        if (!create_directories(config_.log_file_path)) {
            return false;
        }

        // 创建线程池
        thread_pool_ = std::make_shared<spdlog::details::thread_pool>(
            config_.async_queue_size,
            config_.thread_pool_size
        );

        // 创建rotating file sink
        auto rotating_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            config_.log_file_path,
            config_.max_file_size,
            config_.max_files,
            config_.rotate_on_open
        );

        // 创建异步logger
        spdlog::async_overflow_policy overflow_policy;
        switch (config_.overflow_policy) {
            case AsyncOverflowPolicy::BLOCK:
                overflow_policy = spdlog::async_overflow_policy::block;
                break;
            case AsyncOverflowPolicy::OVERRUN_OLDEST:
                overflow_policy = spdlog::async_overflow_policy::overrun_oldest;
                break;
            case AsyncOverflowPolicy::DISCARD_NEW:
                overflow_policy = spdlog::async_overflow_policy::discard_new;
                break;
            default:
                overflow_policy = spdlog::async_overflow_policy::block;
                break;
        }

        logger_ = std::make_shared<spdlog::async_logger>(
            config_.logger_name,
            rotating_sink,
            thread_pool_,
            overflow_policy
        );

        // 设置日志级别
        logger_->set_level(to_spdlog_level(config_.level));

        // 设置格式
        logger_->set_pattern(config_.pattern);

        // 注册logger到spdlog
        spdlog::register_logger(logger_);

        initialized_ = true;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize logger: " << e.what() << std::endl;
        return false;
    }
}

void LoggerSDK::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (!initialized_) {
        return;
    }

    try {
        if (logger_) {
            logger_->flush();
            spdlog::drop(config_.logger_name);
            logger_.reset();
        }

        if (thread_pool_) {
            thread_pool_.reset();
        }

        initialized_ = false;

    } catch (const std::exception& e) {
        std::cerr << "Error during logger shutdown: " << e.what() << std::endl;
    }
}

bool LoggerSDK::is_initialized() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return initialized_;
}

void LoggerSDK::set_level(LogLevel level) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        logger_->set_level(to_spdlog_level(level));
        config_.level = level;
    }
}

LogLevel LoggerSDK::get_level() const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        return from_spdlog_level(logger_->level());
    }
    return config_.level;
}

void LoggerSDK::flush() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        logger_->flush();
    }
}

void LoggerSDK::rotate_now() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        // 获取rotating sink并触发轮转
        auto sinks = logger_->sinks();
        for (auto& sink : sinks) {
            auto rotating_sink = std::dynamic_pointer_cast<spdlog::sinks::rotating_file_sink_mt>(sink);
            if (rotating_sink) {
                rotating_sink->rotate_now();
                break;
            }
        }
    }
}

// 简单日志记录接口实现
void LoggerSDK::trace(const std::string& msg) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        logger_->trace(msg);
    }
}

void LoggerSDK::debug(const std::string& msg) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        logger_->debug(msg);
    }
}

void LoggerSDK::info(const std::string& msg) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        logger_->info(msg);
    }
}

void LoggerSDK::warn(const std::string& msg) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        logger_->warn(msg);
    }
}

void LoggerSDK::error(const std::string& msg) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        logger_->error(msg);
    }
}

void LoggerSDK::critical(const std::string& msg) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (logger_) {
        logger_->critical(msg);
    }
}

LoggerSDK& LoggerSDK::instance() {
    static LoggerSDK instance;
    return instance;
}

LoggerSDK::QueueStats LoggerSDK::get_queue_stats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    QueueStats stats = {0, 0, 0};

    if (thread_pool_) {
        stats.queue_size = thread_pool_->queue_size();
        stats.overrun_counter = thread_pool_->overrun_counter();
        stats.discard_counter = thread_pool_->discard_counter();
    }

    return stats;
}

bool LoggerSDK::create_directories(const std::string& file_path) {
    try {
        std::filesystem::path path(file_path);
        std::filesystem::path dir = path.parent_path();

        if (!dir.empty() && !std::filesystem::exists(dir)) {
            return std::filesystem::create_directories(dir);
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to create directories for: " << file_path
                  << ", error: " << e.what() << std::endl;
        return false;
    }
}

spdlog::level::level_enum LoggerSDK::to_spdlog_level(LogLevel level) const {
    switch (level) {
        case LogLevel::TRACE:    return spdlog::level::trace;
        case LogLevel::DEBUG:    return spdlog::level::debug;
        case LogLevel::INFO:     return spdlog::level::info;
        case LogLevel::WARN:     return spdlog::level::warn;
        case LogLevel::ERROR:    return spdlog::level::err;
        case LogLevel::CRITICAL: return spdlog::level::critical;
        case LogLevel::OFF:      return spdlog::level::off;
        default:                 return spdlog::level::info;
    }
}

LogLevel LoggerSDK::from_spdlog_level(spdlog::level::level_enum level) const {
    switch (level) {
        case spdlog::level::trace:    return LogLevel::TRACE;
        case spdlog::level::debug:    return LogLevel::DEBUG;
        case spdlog::level::info:     return LogLevel::INFO;
        case spdlog::level::warn:     return LogLevel::WARN;
        case spdlog::level::err:      return LogLevel::ERROR;
        case spdlog::level::critical: return LogLevel::CRITICAL;
        case spdlog::level::off:      return LogLevel::OFF;
        default:                      return LogLevel::INFO;
    }
}

} // namespace logger_sdk
