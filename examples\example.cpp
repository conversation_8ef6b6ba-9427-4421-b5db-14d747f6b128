#include "logger_sdk.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace logger_sdk;

void demo_basic_usage() {
    std::cout << "=== 基本使用示例 ===" << std::endl;
    
    // 创建默认配置
    auto config = LoggerConfig::create_default();
    config.log_file_path = "logs/basic_example.log";
    config.level = LogLevel::DEBUG;
    
    // 初始化日志系统
    auto& logger = LoggerSDK::instance();
    if (!logger.initialize(config)) {
        std::cerr << "Failed to initialize logger" << std::endl;
        return;
    }
    
    // 记录不同级别的日志
    logger.trace("This is a trace message");
    logger.debug("This is a debug message");
    logger.info("This is an info message");
    logger.warn("This is a warning message");
    logger.error("This is an error message");
    logger.critical("This is a critical message");
    
    // 格式化日志
    logger.info("User {} logged in from IP {}", "john_doe", "*************");
    logger.warn("Disk usage is {}% on drive {}", 85, "C:");
    
    // 刷新日志
    logger.flush();
    
    std::cout << "基本日志记录完成，请查看 logs/basic_example.log" << std::endl;
}

void demo_rotating_logs() {
    std::cout << "\n=== 文件轮转示例 ===" << std::endl;
    
    // 创建轮转配置 - 小文件大小便于测试
    auto config = LoggerConfig::create_rotating(
        "logs/rotating_example.log", 
        1024,  // 1KB文件大小
        3      // 保留3个文件
    );
    config.level = LogLevel::INFO;
    
    // 创建新的logger实例
    LoggerSDK rotating_logger;
    if (!rotating_logger.initialize(config)) {
        std::cerr << "Failed to initialize rotating logger" << std::endl;
        return;
    }
    
    // 生成大量日志以触发轮转
    for (int i = 0; i < 100; ++i) {
        rotating_logger.info("This is log message number {} with some additional text to make it longer", i);
        
        // 每10条消息手动触发一次轮转
        if (i % 10 == 0) {
            rotating_logger.rotate_now();
        }
    }
    
    rotating_logger.flush();
    std::cout << "轮转日志记录完成，请查看 logs/ 目录下的 rotating_example.log.* 文件" << std::endl;
}

void demo_async_performance() {
    std::cout << "\n=== 异步性能示例 ===" << std::endl;
    
    // 配置高性能异步日志
    auto config = LoggerConfig::create_default();
    config.log_file_path = "logs/async_performance.log";
    config.async_queue_size = 16384;  // 更大的队列
    config.thread_pool_size = 2;      // 2个工作线程
    config.overflow_policy = AsyncOverflowPolicy::OVERRUN_OLDEST;
    config.level = LogLevel::INFO;
    
    LoggerSDK async_logger;
    if (!async_logger.initialize(config)) {
        std::cerr << "Failed to initialize async logger" << std::endl;
        return;
    }
    
    // 测试异步性能
    auto start_time = std::chrono::high_resolution_clock::now();
    
    const int num_messages = 10000;
    for (int i = 0; i < num_messages; ++i) {
        async_logger.info("High performance async log message {} with timestamp {}", 
                         i, std::chrono::duration_cast<std::chrono::milliseconds>(
                             std::chrono::system_clock::now().time_since_epoch()).count());
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // 获取队列统计信息
    auto stats = async_logger.get_queue_stats();
    
    std::cout << "异步日志性能测试完成:" << std::endl;
    std::cout << "- 记录 " << num_messages << " 条消息" << std::endl;
    std::cout << "- 耗时: " << duration.count() << " ms" << std::endl;
    std::cout << "- 平均速度: " << (num_messages * 1000.0 / duration.count()) << " 消息/秒" << std::endl;
    std::cout << "- 队列大小: " << stats.queue_size << std::endl;
    std::cout << "- 溢出计数: " << stats.overrun_counter << std::endl;
    std::cout << "- 丢弃计数: " << stats.discard_counter << std::endl;
    
    async_logger.flush();
}

void demo_multi_thread() {
    std::cout << "\n=== 多线程示例 ===" << std::endl;
    
    auto config = LoggerConfig::create_default();
    config.log_file_path = "logs/multithread_example.log";
    config.level = LogLevel::DEBUG;
    
    LoggerSDK mt_logger;
    if (!mt_logger.initialize(config)) {
        std::cerr << "Failed to initialize multithread logger" << std::endl;
        return;
    }
    
    // 创建多个线程同时写日志
    std::vector<std::thread> threads;
    const int num_threads = 4;
    const int messages_per_thread = 100;
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&mt_logger, t, messages_per_thread]() {
            for (int i = 0; i < messages_per_thread; ++i) {
                mt_logger.info("Thread {} message {}: Processing data item {}", 
                              t, i, i * t);
                
                // 模拟一些工作
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    mt_logger.flush();
    std::cout << "多线程日志记录完成，" << num_threads << " 个线程各记录了 " 
              << messages_per_thread << " 条消息" << std::endl;
}

int main() {
    std::cout << "spdlog SDK 示例程序" << std::endl;
    std::cout << "===================" << std::endl;
    
    try {
        demo_basic_usage();
        demo_rotating_logs();
        demo_async_performance();
        demo_multi_thread();
        
        std::cout << "\n所有示例运行完成！请查看 logs/ 目录下的日志文件。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "示例运行出错: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
